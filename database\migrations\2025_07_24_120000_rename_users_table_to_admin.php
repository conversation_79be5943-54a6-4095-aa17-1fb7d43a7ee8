<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // เปลี่ยนชื่อตาราง users เป็น admin
        Schema::rename('users', 'admin');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // เปลี่ยนชื่อตาราง admin กลับเป็น users
        Schema::rename('admin', 'users');
    }
};
