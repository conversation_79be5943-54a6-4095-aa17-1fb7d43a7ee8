<?php $__env->startSection('title', 'แพ็คเกจ - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ')); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section with Banner Slider -->
<section class="hero-section position-relative <?php echo e($banners->count() === 0 ? 'hero-fallback' : ''); ?>">
    <?php if($banners->count() > 0): ?>
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="4000" data-bs-pause="hover">
            <div class="carousel-inner">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="carousel-item <?php echo e($index === 0 ? 'active' : ''); ?>">
                    <div class="banner-slide" style="background-image: url('<?php echo e(asset('storage/' . $banner->image_path)); ?>');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <?php if($banners->count() > 1): ?>
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="<?php echo e($index); ?>"
                        class="<?php echo e($index === 0 ? 'active' : ''); ?>" aria-current="true" aria-label="Slide <?php echo e($index + 1); ?>"></button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4 text-white">แพ็คเกจบริการ</h1>
                    <p class="lead text-white">แพ็คเกจบริการจัดงานศพที่ครบครันและเหมาะสมกับทุกครอบครัว</p>
                    <?php if($packages->total() > 0): ?>
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-box-open me-2"></i>
                            มีแพ็คเกจทั้งหมด <?php echo e($packages->total()); ?> รายการ
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4">แพ็คเกจบริการ</h1>
                    <p class="lead">แพ็คเกจบริการจัดงานศพที่ครบครันและเหมาะสมกับทุกครอบครัว</p>
                    <?php if($packages->total() > 0): ?>
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-box-open me-2"></i>
                            มีแพ็คเกจทั้งหมด <?php echo e($packages->total()); ?> รายการ
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</section>

<!-- Packages Section -->
<section class="py-5">
    <div class="container">
        <?php if($packages->count() > 0): ?>
        <div class="row g-4">
            <?php $__currentLoopData = $packages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 col-lg-4" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                <div class="card package-card h-100 shadow-sm <?php echo e($package->is_featured ? 'border-warning' : ''); ?>">
                    <?php if($package->is_featured): ?>
                    <div class="card-header bg-warning text-dark text-center fw-bold">
                        <i class="fas fa-star me-2"></i>แพ็คเกจแนะนำ
                    </div>
                    <?php endif; ?>

                    <div class="card-image-container img-size-large position-relative">
                        <?php if($package->image && file_exists(storage_path('app/public/' . $package->image))): ?>
                        <img src="<?php echo e(asset('storage/' . $package->image)); ?>"
                             class="img-fit-contain package-image"
                             alt="<?php echo e($package->name); ?>"
                             style="cursor: pointer;"
                             onclick="window.location.href='<?php echo e(route('packages.show', $package->id)); ?>'">
                        <?php else: ?>
                        <img src="<?php echo e(asset('images/placeholder.svg')); ?>"
                             class="img-fit-contain package-image"
                             alt="ไม่มีรูปภาพ"
                             style="cursor: pointer;"
                             onclick="window.location.href='<?php echo e(route('packages.show', $package->id)); ?>'">
                        <?php endif; ?>

                        <!-- Price badge -->
                        <?php if($package->price_text): ?>
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-success bg-opacity-90 fs-6 px-3 py-2">
                                <?php echo e($package->price_text); ?>

                            </span>
                        </div>
                        <?php endif; ?>

                        <!-- Hover overlay -->
                        <div class="card-hover-overlay">
                            <div class="text-center">
                                <i class="fas fa-eye fa-2x text-white mb-2"></i>
                                <p class="text-white mb-0">คลิกเพื่อดูรายละเอียด</p>
                            </div>
                        </div>
                    </div>

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">
                            <a href="<?php echo e(route('packages.show', $package->id)); ?>" class="text-decoration-none text-dark">
                                <?php echo e($package->name); ?>

                            </a>
                        </h5>
                        <p class="card-text flex-grow-1"><?php echo e($package->description); ?></p>

                        <?php if($package->features): ?>
                        <div class="mb-3">
                            <h6 class="text-muted">รายการที่รวมอยู่ในแพ็คเกจ:</h6>
                            <p class="small text-muted"><?php echo e(Str::limit(strip_tags($package->features), 120)); ?></p>
                        </div>
                        <?php endif; ?>

                        <!-- Package features/highlights -->
                        <?php if($package->duration): ?>
                        <div class="mb-3">
                            <small class="text-success">
                                <i class="fas fa-clock me-1"></i>ระยะเวลา: <?php echo e($package->duration); ?>

                            </small>
                        </div>
                        <?php endif; ?>

                        <div class="mt-auto">
                            <div class="d-grid gap-2">
                                <a href="<?php echo e(route('packages.show', $package->id)); ?>"
                                   class="btn btn-outline-primary btn-hover-effect">
                                    <i class="fas fa-eye me-2"></i>ดูรายละเอียด
                                </a>
                                <a href="<?php echo e(route('contact')); ?>"
                                   class="btn btn-primary btn-hover-effect">
                                    <i class="fas fa-envelope me-2"></i>ติดต่อสอบถาม
                                </a>
                            </div>
                            <small class="text-muted d-block text-center mt-2">
                                <?php if($package->price_text): ?>
                                <i class="fas fa-tag me-1"></i>ราคา: <?php echo e($package->price_text); ?>

                                <?php else: ?>
                                <i class="fas fa-phone me-1"></i>
                                <?php endif; ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <?php if($packages->hasPages()): ?>
        <div class="mt-5">
            <?php echo $__env->make('custom.simple-pagination', ['paginator' => $packages], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
        <?php endif; ?>

        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-dove fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">ยังไม่มีแพ็คเกจ</h3>
            <p class="text-muted">กรุณาติดต่อเราเพื่อสอบถามแพ็คเกจบริการจัดงานศพ</p>
            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">ติดต่อเรา</a>
        </div>
        <?php endif; ?>
    </div>
</section>



<!-- Contact CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอดเวลา</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการสแตนบาย 24 ชั่วโมง
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<style>
/* Package Card Styling - เหมือน Service Card */
.package-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.package-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.package-card .card-body {
    position: relative;
    z-index: 5; /* Ensure card body is above overlay */
}

/* Image Container - เหมือน Service */
.card-image-container {
    position: relative;
    overflow: hidden;
    background: #f8f9fa;
}

.img-size-large {
    height: 250px;
}

.img-fit-contain {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.package-image:hover {
    transform: scale(1.05);
}

/* Hover Overlay */
.card-hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none; /* Allow clicks to pass through */
}

.package-card:hover .card-hover-overlay {
    opacity: 1;
}

/* Button Hover Effects */
.btn-hover-effect {
    transition: all 0.3s ease;
    position: relative;
    z-index: 10; /* Ensure buttons are above overlay */
}

.btn-hover-effect:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.features {
    font-size: 0.9rem;
    line-height: 1.6;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
    });

    // Package card click functionality
    document.querySelectorAll('.package-card').forEach(function(card) {
        card.addEventListener('click', function(e) {
            // Don't navigate if clicking on the button
            if (e.target.closest('.btn')) {
                return;
            }

            const link = card.querySelector('a[href*="packages"]');
            if (link) {
                window.location.href = link.href;
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/frontend/packages.blade.php ENDPATH**/ ?>