<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;

// โหลด Laravel configuration
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔄 สคริปต์เปลี่ยนชื่อตาราง users เป็น admin\n";
echo "===========================================\n\n";

try {
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    echo "🔍 ตรวจสอบการเชื่อมต่อฐานข้อมูล...\n";
    DB::connection()->getPdo();
    echo "✅ การเชื่อมต่อฐานข้อมูล: สำเร็จ\n\n";

    // ตรวจสอบว่าตาราง users มีอยู่หรือไม่
    if (!Schema::hasTable('users')) {
        echo "❌ ไม่พบตาราง 'users' ในฐานข้อมูล\n";
        echo "📝 กรุณาตรวจสอบว่าได้รัน migration พื้นฐานแล้วหรือไม่\n";
        exit(1);
    }

    // ตรวจสอบว่าตาราง admin มีอยู่แล้วหรือไม่
    if (Schema::hasTable('admin')) {
        echo "⚠️ ตาราง 'admin' มีอยู่แล้ว\n";
        echo "❓ ต้องการลบตาราง admin เดิมและสร้างใหม่หรือไม่? (พิมพ์ 'YES' เพื่อยืนยัน): ";
        
        $handle = fopen("php://stdin", "r");
        $confirmation = trim(fgets($handle));
        fclose($handle);

        if ($confirmation !== 'YES') {
            echo "❌ ยกเลิกการดำเนินการ\n";
            exit(0);
        }

        echo "🗑️ ลบตาราง admin เดิม...\n";
        Schema::dropIfExists('admin');
        echo "✅ ลบตาราง admin เดิมสำเร็จ\n\n";
    }

    // สำรองข้อมูลจากตาราง users
    echo "💾 สำรองข้อมูลจากตาราง users...\n";
    $users = DB::table('users')->get();
    echo "📊 พบข้อมูล: " . $users->count() . " รายการ\n\n";

    // รัน migration เปลี่ยนชื่อตาราง
    echo "🚀 รัน migration เปลี่ยนชื่อตาราง...\n";
    Artisan::call('migrate', [
        '--path' => 'database/migrations/2025_07_24_120000_rename_users_table_to_admin.php',
        '--force' => true
    ]);
    echo "✅ เปลี่ยนชื่อตารางสำเร็จ: users → admin\n\n";

    // ตรวจสอบผลลัพธ์
    echo "🔍 ตรวจสอบผลลัพธ์...\n";
    
    if (Schema::hasTable('admin')) {
        echo "✅ ตาราง 'admin' ถูกสร้างสำเร็จ\n";
        
        // ตรวจสอบข้อมูลในตาราง admin
        $adminCount = DB::table('admin')->count();
        echo "📊 จำนวนข้อมูลในตาราง admin: " . $adminCount . " รายการ\n";
        
        if ($adminCount === $users->count()) {
            echo "✅ ข้อมูลถูกย้ายครบถ้วน\n";
        } else {
            echo "⚠️ จำนวนข้อมูลไม่ตรงกัน อาจมีปัญหาในการย้ายข้อมูล\n";
        }
    } else {
        echo "❌ ไม่พบตาราง 'admin' หลังจากรัน migration\n";
        exit(1);
    }

    if (!Schema::hasTable('users')) {
        echo "✅ ตาราง 'users' ถูกเปลี่ยนชื่อสำเร็จ\n";
    } else {
        echo "⚠️ ตาราง 'users' ยังคงมีอยู่ อาจมีปัญหาในการเปลี่ยนชื่อ\n";
    }

    echo "\n🎉 การเปลี่ยนชื่อตารางเสร็จสมบูรณ์!\n";
    echo "==========================================\n";
    echo "📋 สรุปการเปลี่ยนแปลง:\n";
    echo "   ✅ ตาราง: users → admin\n";
    echo "   ✅ Model: User → Admin\n";
    echo "   ✅ AuthController: ใช้ Admin Model\n";
    echo "   ✅ config/auth.php: ใช้ Admin Model\n";
    echo "   ✅ AdminUserSeeder: ใช้ Admin Model\n\n";

    echo "🔧 ขั้นตอนต่อไป:\n";
    echo "1. ทดสอบการเข้าสู่ระบบ Admin\n";
    echo "2. อัปเดต Data Dictionary\n";
    echo "3. ลบไฟล์ User Model เดิม (ถ้าต้องการ)\n";
    echo "4. ลบไฟล์ UserFactory เดิม (ถ้าต้องการ)\n\n";

    echo "📝 ข้อมูลการเข้าสู่ระบบ:\n";
    echo "   Email: <EMAIL>\n";
    echo "   Password: 123456789\n\n";

} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "📍 ไฟล์: " . $e->getFile() . " บรรทัด: " . $e->getLine() . "\n\n";
    
    echo "🔧 วิธีแก้ไข:\n";
    echo "1. ตรวจสอบการตั้งค่าฐานข้อมูลในไฟล์ .env\n";
    echo "2. ตรวจสอบว่า MySQL Server ทำงานอยู่\n";
    echo "3. ตรวจสอบว่าฐานข้อมูลมีอยู่จริง\n";
    echo "4. ตรวจสอบสิทธิ์การเข้าถึงฐานข้อมูล\n\n";
    
    echo "📝 หากยังมีปัญหา สามารถเปลี่ยนชื่อด้วย SQL โดยตรง:\n";
    echo "RENAME TABLE users TO admin;\n";
}

echo "\n🏁 สคริปต์สิ้นสุด\n";
